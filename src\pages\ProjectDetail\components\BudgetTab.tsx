import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Wallet, Plus, Loader2 } from "lucide-react";
import { Loading } from "@/components/ui/loaders";
import { formatDate } from "@/utils";
import { getStatusColor, formatVND } from "../shared/utils";
import { Transaction } from "@/types/transaction";
import { UserRole } from "@/contexts/auth-types";
import { useAuth } from "@/contexts";
import { toast } from "sonner";
import { useCreateTransaction } from "@/hooks/queries/transaction";
import { useGetEvaluationsByProjectId } from "@/hooks/queries/evaluation";

interface BudgetTabProps {
  projectId: string;
  category: string;
  transactions: Transaction[];
}

// Transaction request form interface
interface TransactionRequest {
  title: string;
  type: string;
  "receiver-account": string;
  "receiver-name": string;
  "receiver-bank-name": string;
  "transfer-content": string;
  "total-money": number;
  "pay-method": string;
  status: string;
  "evaluation-stage-id"?: string;
}

// Transaction types available for request
const TRANSACTION_TYPES = [
  { value: "project", label: "Project" },
  { value: "evaluationstage", label: "Evaluation Stage" },
];

const BudgetTab: React.FC<BudgetTabProps> = ({
  projectId,
  category,
  transactions,
}) => {
  const [isLoading] = useState(false);
  const [showRequestDialog, setShowRequestDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [requestForm, setRequestForm] = useState<TransactionRequest>({
    title: "",
    type: "",
    "receiver-account": "",
    "receiver-name": "",
    "receiver-bank-name": "",
    "transfer-content": "",
    "total-money": 0,
    "pay-method": "bank_transfer", // Default payment method
    status: "created",
  });

  // Auth hook to check user role
  const { user } = useAuth();

  // API hooks
  const createTransaction = useCreateTransaction();
  const { data: evaluationsResponse } = useGetEvaluationsByProjectId(projectId);

  // Determine project type
  const categoryLower = category?.toLowerCase() || "";
  const isBasicCategory = categoryLower === "basic";
  const isApplicationCategory =
    categoryLower === "application" ||
    categoryLower === "implementation" ||
    categoryLower.includes("application") ||
    categoryLower.includes("implementation");

  // Get evaluation stages for application projects
  const evaluationStages =
    evaluationsResponse?.["data-list"]?.[0]?.["evaluation-stages"] || [];

  const handleRequestTransaction = () => {
    setShowRequestDialog(true);
  };

  const handleCloseDialog = () => {
    setShowRequestDialog(false);
    setRequestForm({
      title: "",
      type: "",
      "receiver-account": "",
      "receiver-name": "",
      "receiver-bank-name": "",
      "transfer-content": "",
      "total-money": 0,
      "pay-method": "bank_transfer", // Default payment method
      status: "created",
    });
  };

  const handleInputChange = (
    field: keyof TransactionRequest,
    value: string | number
  ) => {
    if (field === "total-money") {
      // Remove non-numeric characters except decimal point
      const numericValue =
        typeof value === "string" ? value.replace(/[^0-9]/g, "") : value;
      setRequestForm((prev) => ({
        ...prev,
        [field]: Number(numericValue) || 0,
      }));
    } else {
      setRequestForm((prev) => ({
        ...prev,
        [field]: value,
      }));
    }
  };

  const handleSubmitRequest = async () => {
    // Validation
    if (!requestForm.title.trim()) {
      toast.error("Please enter a request title");
      return;
    }

    if (!requestForm.type) {
      toast.error("Please select a transaction type");
      return;
    }

    if (!requestForm["receiver-account"].trim()) {
      toast.error("Please enter receiver account");
      return;
    }

    if (!requestForm["receiver-name"].trim()) {
      toast.error("Please enter receiver name");
      return;
    }

    if (!requestForm["receiver-bank-name"].trim()) {
      toast.error("Please enter receiver bank name");
      return;
    }

    if (!requestForm["transfer-content"].trim()) {
      toast.error("Please enter transfer content");
      return;
    }

    if (!requestForm["total-money"] || requestForm["total-money"] <= 0) {
      toast.error("Please enter a valid amount");
      return;
    }

    // For application projects, validate evaluation stage selection
    if (isApplicationCategory && requestForm.type === "evaluationstage") {
      if (!requestForm["evaluation-stage-id"]) {
        toast.error("Please select an evaluation stage");
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Prepare transaction data - always include project-id
      const transactionData: {
        title: string;
        type: string;
        "receiver-account": string;
        "receiver-name": string;
        "receiver-bank-name": string;
        "transfer-content": string;
        "total-money": number;
        "pay-method": string;
        status: string;
        "project-id": string;
        "evaluation-stage-id"?: string;
      } = {
        title: requestForm.title,
        type: requestForm.type,
        "receiver-account": requestForm["receiver-account"],
        "receiver-name": requestForm["receiver-name"],
        "receiver-bank-name": requestForm["receiver-bank-name"],
        "transfer-content": requestForm["transfer-content"],
        "total-money": requestForm["total-money"],
        "pay-method": requestForm["pay-method"],
        status: requestForm.status,
        "project-id": projectId, // Always include project-id
      };

      // Add evaluation-stage-id for application projects
      if (isApplicationCategory && requestForm["evaluation-stage-id"]) {
        transactionData["evaluation-stage-id"] =
          requestForm["evaluation-stage-id"];
      }

      await createTransaction.mutateAsync(transactionData);
      handleCloseDialog();
    } catch (error) {
      console.error("Failed to submit transaction request:", error);
      // Error is already handled by the mutation
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <Loading />
      </div>
    );
  }

  return (
    <Card className="shadow-sm">
      <CardHeader className="pb-4 sm:pb-6">
        <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3 sm:gap-4">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg sm:text-xl font-semibold text-gray-900">
              Budget Overview
            </CardTitle>
            <CardDescription className="text-sm sm:text-base mt-1">
              {isBasicCategory
                ? "Project budget allocation and expense tracking for basic research"
                : "Project budget allocation and expense tracking for application projects"}
            </CardDescription>
          </div>
          {user?.role === UserRole.PRINCIPAL_INVESTIGATOR && (
            <Button
              onClick={handleRequestTransaction}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              <Plus className="w-4 h-4 mr-2" />
              Request Transaction
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4 sm:space-y-6 pt-0">
        {/* Transactions Table Section */}
        <div>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[200px]">Transaction</TableHead>
                  <TableHead className="min-w-[100px]">Type</TableHead>
                  <TableHead className="min-w-[80px]">Amount</TableHead>
                  <TableHead className="min-w-[100px]">Date</TableHead>
                  <TableHead className="min-w-[80px]">Status</TableHead>
                  <TableHead className="text-right min-w-[80px]">
                    Payment Method
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.slice(0, 5).map((transaction) => (
                  <TableRow key={transaction.code}>
                    <TableCell>
                      <div>
                        <p className="font-medium text-sm sm:text-base break-words">
                          {transaction.title}
                        </p>
                        <p className="text-xs sm:text-sm text-muted-foreground">
                          {transaction.code}
                        </p>
                        {transaction.description && (
                          <p className="text-xs sm:text-sm text-muted-foreground mt-1">
                            {transaction.description}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {transaction.type}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatVND(transaction.amount)}
                    </TableCell>
                    <TableCell className="text-sm">
                      {formatDate(transaction.createdAt)}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={`${getStatusColor(
                          transaction.status
                        )} text-xs`}
                      >
                        {transaction.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Badge variant="outline" className="text-xs">
                        {transaction.paymentMethod}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {transactions.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Wallet className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium mb-2">
                  No transactions found
                </p>
                <p className="text-sm text-muted-foreground">
                  No financial transactions have been recorded for this project
                  yet.
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>

      {/* Request Transaction Dialog */}
      <Dialog open={showRequestDialog} onOpenChange={handleCloseDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Wallet className="w-5 h-5 text-blue-600" />
              Request Transaction
            </DialogTitle>
            <DialogDescription>
              Submit a new transaction request for this{" "}
              {isBasicCategory ? "project" : "evaluation stage"}. Please provide
              the details below.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="request-title" className="text-sm font-medium">
                Request Title <span className="text-red-500">*</span>
              </Label>
              <Input
                id="request-title"
                value={requestForm.title}
                onChange={(e) => handleInputChange("title", e.target.value)}
                placeholder="Enter request title"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="transaction-type" className="text-sm font-medium">
                Transaction Type <span className="text-red-500">*</span>
              </Label>
              <Select
                onValueChange={(value) => handleInputChange("type", value)}
                value={requestForm.type}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select transaction type" />
                </SelectTrigger>
                <SelectContent>
                  {TRANSACTION_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Evaluation Stage Selection for Application Projects */}
            {isApplicationCategory &&
              requestForm.type === "evaluationstage" && (
                <div>
                  <Label
                    htmlFor="evaluation-stage"
                    className="text-sm font-medium"
                  >
                    Evaluation Stage <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange("evaluation-stage-id", value)
                    }
                    value={requestForm["evaluation-stage-id"] || ""}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select evaluation stage" />
                    </SelectTrigger>
                    <SelectContent>
                      {evaluationStages.map((stage) => (
                        <SelectItem key={stage.id} value={stage.id}>
                          {stage.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label
                  htmlFor="receiver-account"
                  className="text-sm font-medium"
                >
                  Receiver Account <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="receiver-account"
                  value={requestForm["receiver-account"]}
                  onChange={(e) =>
                    handleInputChange("receiver-account", e.target.value)
                  }
                  placeholder="Enter receiver account number"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="receiver-name" className="text-sm font-medium">
                  Receiver Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="receiver-name"
                  value={requestForm["receiver-name"]}
                  onChange={(e) =>
                    handleInputChange("receiver-name", e.target.value)
                  }
                  placeholder="Enter receiver name"
                  className="mt-1"
                />
              </div>
            </div>

            <div>
              <Label
                htmlFor="receiver-bank-name"
                className="text-sm font-medium"
              >
                Receiver Bank Name <span className="text-red-500">*</span>
              </Label>
              <Input
                id="receiver-bank-name"
                value={requestForm["receiver-bank-name"]}
                onChange={(e) =>
                  handleInputChange("receiver-bank-name", e.target.value)
                }
                placeholder="Enter receiver bank name"
                className="mt-1"
              />
            </div>

            <div>
              <Label htmlFor="transfer-content" className="text-sm font-medium">
                Transfer Content <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="transfer-content"
                value={requestForm["transfer-content"]}
                onChange={(e) =>
                  handleInputChange("transfer-content", e.target.value)
                }
                placeholder="Enter transfer content/description"
                className="mt-1"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="amount" className="text-sm font-medium">
                Amount (VND) <span className="text-red-500">*</span>
              </Label>
              <div className="relative mt-1">
                <Input
                  id="amount"
                  type="text"
                  value={requestForm["total-money"] || ""}
                  onChange={(e) =>
                    handleInputChange("total-money", e.target.value)
                  }
                  placeholder="Enter amount"
                  className="pr-12"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <span className="text-gray-500 text-sm">VND</span>
                </div>
              </div>
              {requestForm["total-money"] && (
                <p className="text-xs text-gray-500 mt-1">
                  Amount: {formatVND(requestForm["total-money"])}
                </p>
              )}
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={handleCloseDialog}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitRequest}
              disabled={
                isSubmitting ||
                !requestForm.title.trim() ||
                !requestForm.type ||
                !requestForm["receiver-account"].trim() ||
                !requestForm["receiver-name"].trim() ||
                !requestForm["receiver-bank-name"].trim() ||
                !requestForm["transfer-content"].trim() ||
                !requestForm["total-money"] ||
                (isApplicationCategory &&
                  requestForm.type === "evaluationstage" &&
                  !requestForm["evaluation-stage-id"])
              }
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Submitting...
                </>
              ) : (
                "Submit Request"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default BudgetTab;
